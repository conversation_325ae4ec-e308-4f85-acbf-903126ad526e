import { useColorScheme } from 'react-native';

type ThemeColors = {
  primary: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  };
  success: {
    100: string;
    500: string;
    700: string;
  };
  warning: {
    100: string;
    500: string;
    700: string;
  };
  error: {
    100: string;
    500: string;
    700: string;
  };
  background: string;
  card: string;
  text: string;
  textLight: string;
  border: string;
  white: string;
  black: string;
};

export function useTheme() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const lightColors: ThemeColors = {
    primary: {
      50: '#EFF6FF',
      100: '#DBEAFE',
      200: '#BFDBFE',
      300: '#93C5FD',
      400: '#60A5FA',
      500: '#3B82F6',
      600: '#2563EB',
      700: '#1D4ED8',
      800: '#1E40AF',
      900: '#1E3A8A',
    },
    success: {
      100: '#D1FAE5',
      500: '#10B981',
      700: '#047857',
    },
    warning: {
      100: '#FEF3C7',
      500: '#F59E0B',
      700: '#B45309',
    },
    error: {
      100: '#FEE2E2',
      500: '#EF4444',
      700: '#B91C1C',
    },
    background: '#F9FAFB',
    card: '#FFFFFF',
    text: '#1F2937',
    textLight: '#6B7280',
    border: '#E5E7EB',
    white: '#FFFFFF',
    black: '#000000',
  };

  const darkColors: ThemeColors = {
    primary: {
      50: '#1F2937',
      100: '#374151',
      200: '#1E40AF',
      300: '#1D4ED8',
      400: '#2563EB',
      500: '#3B82F6',
      600: '#60A5FA',
      700: '#93C5FD',
      800: '#BFDBFE',
      900: '#DBEAFE',
    },
    success: {
      100: '#064E3B',
      500: '#10B981',
      700: '#D1FAE5',
    },
    warning: {
      100: '#78350F',
      500: '#F59E0B',
      700: '#FEF3C7',
    },
    error: {
      100: '#7F1D1D',
      500: '#EF4444',
      700: '#FEE2E2',
    },
    background: '#111827',
    card: '#1F2937',
    text: '#F9FAFB',
    textLight: '#9CA3AF',
    border: '#374151',
    white: '#FFFFFF',
    black: '#000000',
  };

  return {
    colors: isDark ? darkColors : lightColors,
    isDark,
  };
}