**Candidate Profile Feature Specification – Section-wise Data Fields & UI/UX Guide**

---

### 1. 👤 Personal Information

**Fields:**

* Full Name *(text)*
* Profile Photo *(image upload with crop)*
* Gender *(dropdown: Male, Female, Other)*
* Date of Birth *(date picker)*
* Nationality *(autocomplete dropdown)*
* Current Country & City *(autocomplete + Google Places)*
* Contact Number *(country code + mobile input)*
* Email Address *(email field)*
* WhatsApp Number *(optional)*
* LinkedIn Profile *(URL)*
* Personal Website / Portfolio *(URL, optional)*

**UI/UX Tips:**

* Auto-fill location from device
* Flag icons for phone input
* Preview profile image crop

---

### 2. 🌍 Job Preferences

**Fields:**

* Seeking: *(radio group)*

  * Overseas Opportunities
  * Domestic Jobs (e.g., within India)
* Preferred Work Countries *(multi-select with flags)*
* Preferred Locations *(state/city-wise for domestic jobs)*
* Job Types *(multi-select: Full-time, Contract, Remote, On-site)*
* Roles Interested In *(tag input)*
* Industry *(multi-select or dropdown)*
* Expected Salary *(number + currency)*
* Willing to Relocate *(Yes/No toggle)*
* Available From *(date picker or notice period)*

**UI/UX Tips:**

* Conditional fields based on job preference type
* Use chips/tags for multi-select
* "Decide Later" option to skip

---

### 3. 📂 Summary / Objective

**Fields:**

* Career Summary *(multi-line input, optional AI assist)*
* Years of Experience *(numeric or slider)*
* Highlights *(short bullet/point form entry)*

**UI/UX Tips:**

* Pre-filled examples based on role

---

### 4. 🧬 Skills & Tools

**Fields:**

* Primary Skills *(tag input + dropdown of common terms)*
* Secondary Skills *(optional)*
* Tools/Software Proficiency *(multi-select)*
* Skill Level *(dropdown: Beginner, Intermediate, Advanced)*

**UI/UX Tips:**

* Drag-to-reorder for priority
* Group by categories

---

### 5. 💼 Work Experience

**Fields per entry:**

* Job Title
* Company Name
* Country, City
* Start Date – End Date
* Current Job *(toggle if still working)*
* Roles & Responsibilities *(multi-line)*
* Achievements *(bullet-style, optional)*
* Industry / Domain *(dropdown)*
* Upload Experience Letter *(optional)*

**UI/UX Tips:**

* Vertical timeline or card layout
* Expand/collapse entries

---

### 6. 🎓 Education

**Fields per entry:**

* Degree / Qualification
* Specialization
* Institution
* Country / State
* Year of Passing
* Grade / GPA *(optional)*
* Upload Degree Certificate *(file upload)*

**UI/UX Tips:**

* Autocomplete university list
* Tag icons for verified records

---

### 7. 📓 Certifications & Training

**Fields:**

* Certification Name
* Issued By
* Date of Issue / Expiry *(date picker)*
* Upload Certificate *(PDF/image)*

**UI/UX Tips:**

* Valid / Expired status tagging

---

### 8. 🛍️ Projects / Portfolio

**Fields per entry:**

* Project Title
* Role / Position
* Project Duration
* Technologies Used
* Key Outcomes
* Link / URL *(optional)*
* File Upload *(optional)*

**UI/UX Tips:**

* Card layout with preview support

---

### 9. 🌐 Languages

**Fields:**

* Language Name
* Reading / Writing / Speaking Levels *(dropdown or sliders)*

**UI/UX Tips:**

* Show icons for each language
* Visual rating scale

---

### 10. 🏛️ International Readiness / Visa

*(Shown only if “Overseas Job” selected)*

**Fields:**

* Passport Number
* Passport Valid Till *(date picker)*
* Visa Status *(dropdown: Visit, Work, Student, None)*
* Visa Country *(dropdown)*
* Previous Overseas Experience *(Yes/No toggle)*
* Country Names *(if yes)*

**UI/UX Tips:**

* Hide this section for domestic-only candidates

---

### 11. 📅 Documents

**Fields:**

* Upload Resume / CV *(mandatory)*
* Upload Photo ID (e.g., Aadhaar, Passport) *(optional)*
* Upload Experience Letters *(optional)*
* Upload Degree Certificates *(optional)*
* Other Relevant Documents *(multi-upload)*

**UI/UX Tips:**

* Drag-and-drop upload
* Document preview thumbnails
* Upload status indicators

---

### 12. ✍️ Additional Information

**Fields:**

* Willing to Work Overtime / Rotational Shifts *(Yes/No)*
* Medical Restrictions *(optional text)*
* References *(Name & Contact Info)*
* Open to Freelance/Contract *(Yes/No toggle)*

**UI/UX Tips:**

* Optional block with collapsible section

---

### 13. 🔐 Admin/Recruiter-Only View

**Fields:**

* Profile Verified *(checkbox)*
* Assigned Recruiter *(dropdown)*
* Candidate Source *(LinkedIn, Referral, etc.)*
* Tags *(e.g., Hot, On-hold, Interviewed)*

---

### 🛍️ Onboarding UX Flow (Suggested Steps)

1. Welcome & Profile Photo Upload
2. Basic Info + Contact
3. Job Preferences
4. Education + Experience
5. Skills + Certifications
6. Document Upload
7. Review & Final Submit

Each step should feature:

* Progress bar
* Save Draft
* Responsive layout (mobile/desktop)
* Real-time validation
* "Preview Profile" feature
