import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { BellRing } from 'lucide-react-native';

interface ScreenHeaderProps {
  title: string;
  showNotification?: boolean;
  onNotificationPress?: () => void;
}

const ScreenHeader: React.FC<ScreenHeaderProps> = ({
  title,
  showNotification = true,
  onNotificationPress,
}) => {
  const { colors } = useTheme();

  return (
    <View style={[styles.container, { borderBottomColor: colors.border }]}>
      <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
      {showNotification && (
        <TouchableOpacity 
          style={[styles.notificationButton, { backgroundColor: colors.card }]} 
          onPress={onNotificationPress}
        >
          <BellRing size={20} color={colors.textLight} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  title: {
    fontFamily: 'Poppins-Bold',
    fontSize: 24,
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ScreenHeader;