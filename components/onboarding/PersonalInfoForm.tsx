import React from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, Image } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { useOnboarding } from '@/context/OnboardingContext';
import { Camera, UserCircle } from 'lucide-react-native';

const PersonalInfoForm = () => {
  const { colors } = useTheme();
  const { profileData, updateProfileData } = useOnboarding();

  const handleImagePicker = () => {
    // In a real app, this would use expo-image-picker
    // For now, just set a placeholder image
    updateProfileData({
      photoUri: 'https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg'
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.photoContainer}>
        <TouchableOpacity 
          style={[styles.photoUpload, { backgroundColor: colors.primary[100] }]} 
          onPress={handleImagePicker}
        >
          {profileData.photoUri ? (
            <Image 
              source={{ uri: profileData.photoUri }} 
              style={styles.profilePhoto} 
            />
          ) : (
            <UserCircle size={64} color={colors.primary[600]} />
          )}
          <View style={[styles.cameraButton, { backgroundColor: colors.primary[600] }]}>
            <Camera size={16} color={colors.white} />
          </View>
        </TouchableOpacity>
        <Text style={[styles.photoText, { color: colors.textLight }]}>Profile Photo</Text>
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Full Name</Text>
        <TextInput
          style={[styles.input, { 
            backgroundColor: colors.card, 
            borderColor: colors.border,
            color: colors.text 
          }]}
          value={profileData.fullName}
          onChangeText={(text) => updateProfileData({ fullName: text })}
          placeholder="Enter your full name"
          placeholderTextColor={colors.textLight}
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Email Address</Text>
        <TextInput
          style={[styles.input, { 
            backgroundColor: colors.card, 
            borderColor: colors.border,
            color: colors.text 
          }]}
          value={profileData.email}
          onChangeText={(text) => updateProfileData({ email: text })}
          placeholder="Enter your email address"
          placeholderTextColor={colors.textLight}
          keyboardType="email-address"
          autoCapitalize="none"
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Phone Number</Text>
        <TextInput
          style={[styles.input, { 
            backgroundColor: colors.card, 
            borderColor: colors.border,
            color: colors.text 
          }]}
          value={profileData.phone}
          onChangeText={(text) => updateProfileData({ phone: text })}
          placeholder="Enter your phone number"
          placeholderTextColor={colors.textLight}
          keyboardType="phone-pad"
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Current Location</Text>
        <TextInput
          style={[styles.input, { 
            backgroundColor: colors.card, 
            borderColor: colors.border,
            color: colors.text 
          }]}
          value={profileData.location}
          onChangeText={(text) => updateProfileData({ location: text })}
          placeholder="City, Country"
          placeholderTextColor={colors.textLight}
        />
      </View>
      
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>LinkedIn Profile (Optional)</Text>
        <TextInput
          style={[styles.input, { 
            backgroundColor: colors.card, 
            borderColor: colors.border,
            color: colors.text 
          }]}
          value={profileData.linkedin}
          onChangeText={(text) => updateProfileData({ linkedin: text })}
          placeholder="linkedin.com/in/username"
          placeholderTextColor={colors.textLight}
          autoCapitalize="none"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 20,
  },
  photoContainer: {
    alignItems: 'center',
    marginBottom: 10,
  },
  photoUpload: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    position: 'relative',
  },
  profilePhoto: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  formGroup: {
    marginBottom: 4,
  },
  label: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    marginBottom: 8,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
});

export default PersonalInfoForm;