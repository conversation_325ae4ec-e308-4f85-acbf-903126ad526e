import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { useOnboarding } from '@/context/OnboardingContext';
import { X } from 'lucide-react-native';

const JobPreferencesForm = () => {
  const { colors } = useTheme();
  const { profileData, updateProfileData } = useOnboarding();
  const [newCountry, setNewCountry] = useState('');
  const [newRole, setNewRole] = useState('');

  const handleAddCountry = () => {
    if (newCountry.trim() === '') return;
    
    const updatedCountries = [...profileData.preferredCountries, newCountry];
    updateProfileData({ preferredCountries: updatedCountries });
    setNewCountry('');
  };

  const handleRemoveCountry = (index: number) => {
    const updatedCountries = [...profileData.preferredCountries];
    updatedCountries.splice(index, 1);
    updateProfileData({ preferredCountries: updatedCountries });
  };

  const handleAddRole = () => {
    if (newRole.trim() === '') return;
    
    const updatedRoles = profileData.preferredRoles ? 
      [...profileData.preferredRoles, newRole] : 
      [newRole];
    
    updateProfileData({ preferredRoles: updatedRoles });
    setNewRole('');
  };

  const handleRemoveRole = (index: number) => {
    if (!profileData.preferredRoles) return;
    
    const updatedRoles = [...profileData.preferredRoles];
    updatedRoles.splice(index, 1);
    updateProfileData({ preferredRoles: updatedRoles });
  };

  const toggleJobType = (type: string) => {
    updateProfileData({ jobType: type });
  };

  return (
    <View style={styles.container}>
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Job Type</Text>
        <View style={styles.jobTypeContainer}>
          {['Full-time', 'Part-time', 'Contract', 'Remote'].map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.jobTypeButton,
                profileData.jobType === type ? 
                  { backgroundColor: colors.primary[600] } : 
                  { backgroundColor: colors.card, borderColor: colors.border }
              ]}
              onPress={() => toggleJobType(type)}
            >
              <Text 
                style={[
                  styles.jobTypeText, 
                  { color: profileData.jobType === type ? colors.white : colors.text }
                ]}
              >
                {type}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Expected Salary</Text>
        <TextInput
          style={[styles.input, { 
            backgroundColor: colors.card, 
            borderColor: colors.border,
            color: colors.text 
          }]}
          value={profileData.expectedSalary}
          onChangeText={(text) => updateProfileData({ expectedSalary: text })}
          placeholder="e.g. $80,000 - $100,000"
          placeholderTextColor={colors.textLight}
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Preferred Countries</Text>
        <View style={styles.tagContainer}>
          {profileData.preferredCountries.map((country, index) => (
            <View key={index} style={[styles.tag, { backgroundColor: colors.primary[100] }]}>
              <Text style={[styles.tagText, { color: colors.primary[700] }]}>{country}</Text>
              <TouchableOpacity onPress={() => handleRemoveCountry(index)}>
                <X size={16} color={colors.primary[700]} />
              </TouchableOpacity>
            </View>
          ))}
        </View>
        <View style={styles.tagInputContainer}>
          <TextInput
            style={[styles.tagInput, { 
              backgroundColor: colors.card, 
              borderColor: colors.border,
              color: colors.text 
            }]}
            value={newCountry}
            onChangeText={setNewCountry}
            placeholder="Add a country"
            placeholderTextColor={colors.textLight}
            onSubmitEditing={handleAddCountry}
          />
          <TouchableOpacity 
            style={[styles.addButton, { backgroundColor: colors.primary[600] }]}
            onPress={handleAddCountry}
          >
            <Text style={[styles.addButtonText, { color: colors.white }]}>Add</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Roles Interested In</Text>
        <View style={styles.tagContainer}>
          {profileData.preferredRoles?.map((role, index) => (
            <View key={index} style={[styles.tag, { backgroundColor: colors.primary[100] }]}>
              <Text style={[styles.tagText, { color: colors.primary[700] }]}>{role}</Text>
              <TouchableOpacity onPress={() => handleRemoveRole(index)}>
                <X size={16} color={colors.primary[700]} />
              </TouchableOpacity>
            </View>
          ))}
        </View>
        <View style={styles.tagInputContainer}>
          <TextInput
            style={[styles.tagInput, { 
              backgroundColor: colors.card, 
              borderColor: colors.border,
              color: colors.text 
            }]}
            value={newRole}
            onChangeText={setNewRole}
            placeholder="Add a role"
            placeholderTextColor={colors.textLight}
            onSubmitEditing={handleAddRole}
          />
          <TouchableOpacity 
            style={[styles.addButton, { backgroundColor: colors.primary[600] }]}
            onPress={handleAddRole}
          >
            <Text style={[styles.addButtonText, { color: colors.white }]}>Add</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.switchContainer}>
        <Text style={[styles.label, { color: colors.text }]}>Willing to Relocate</Text>
        <TouchableOpacity
          style={[
            styles.toggleButton,
            { backgroundColor: profileData.willingToRelocate ? colors.primary[600] : colors.card }
          ]}
          onPress={() => updateProfileData({ willingToRelocate: !profileData.willingToRelocate })}
        >
          <View 
            style={[
              styles.toggleIndicator, 
              { 
                backgroundColor: colors.white,
                transform: [{ translateX: profileData.willingToRelocate ? 20 : 0 }]
              }
            ]} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 20,
  },
  formGroup: {
    marginBottom: 8,
  },
  label: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    marginBottom: 8,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  jobTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  jobTypeButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
  },
  jobTypeText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
    gap: 8,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 8,
  },
  tagText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 12,
  },
  tagInputContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  tagInput: {
    flex: 1,
    height: 46,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  addButton: {
    paddingHorizontal: 16,
    height: 46,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButtonText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  toggleButton: {
    width: 50,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    paddingHorizontal: 5,
  },
  toggleIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
});

export default JobPreferencesForm;