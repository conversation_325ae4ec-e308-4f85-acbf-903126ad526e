import React from 'react';
import { View, Text, StyleSheet, TextInput } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { useOnboarding } from '@/context/OnboardingContext';

const EducationForm = () => {
  const { colors } = useTheme();
  const { profileData, updateProfileData } = useOnboarding();

  return (
    <View style={styles.container}>
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Degree / Qualification</Text>
        <TextInput
          style={[styles.input, { 
            backgroundColor: colors.card, 
            borderColor: colors.border,
            color: colors.text 
          }]}
          value={profileData.degree}
          onChangeText={(text) => updateProfileData({ degree: text })}
          placeholder="e.g. Bachelor of Science in Computer Science"
          placeholderTextColor={colors.textLight}
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Institution</Text>
        <TextInput
          style={[styles.input, { 
            backgroundColor: colors.card, 
            borderColor: colors.border,
            color: colors.text 
          }]}
          value={profileData.institution}
          onChangeText={(text) => updateProfileData({ institution: text })}
          placeholder="e.g. University of Technology"
          placeholderTextColor={colors.textLight}
        />
      </View>

      <View style={styles.rowContainer}>
        <View style={[styles.formGroup, { flex: 1 }]}>
          <Text style={[styles.label, { color: colors.text }]}>Graduation Year</Text>
          <TextInput
            style={[styles.input, { 
              backgroundColor: colors.card, 
              borderColor: colors.border,
              color: colors.text 
            }]}
            value={profileData.graduationYear}
            onChangeText={(text) => updateProfileData({ graduationYear: text })}
            placeholder="e.g. 2022"
            placeholderTextColor={colors.textLight}
            keyboardType="number-pad"
          />
        </View>

        <View style={[styles.formGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={[styles.label, { color: colors.text }]}>GPA / Grade (Optional)</Text>
          <TextInput
            style={[styles.input, { 
              backgroundColor: colors.card, 
              borderColor: colors.border,
              color: colors.text 
            }]}
            value={profileData.gpa}
            onChangeText={(text) => updateProfileData({ gpa: text })}
            placeholder="e.g. 3.8/4.0"
            placeholderTextColor={colors.textLight}
          />
        </View>
      </View>

      <View style={styles.infoContainer}>
        <Text style={[styles.infoTitle, { color: colors.text }]}>Tips for Education Section</Text>
        <Text style={[styles.infoText, { color: colors.textLight }]}>
          • Add your highest level of education first
        </Text>
        <Text style={[styles.infoText, { color: colors.textLight }]}>
          • Include relevant courses or specializations
        </Text>
        <Text style={[styles.infoText, { color: colors.textLight }]}>
          • You can add certificates in the next section
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 20,
  },
  formGroup: {
    marginBottom: 8,
  },
  label: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    marginBottom: 8,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  rowContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  infoContainer: {
    backgroundColor: '#F8FAFC',
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  infoTitle: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
    marginBottom: 8,
  },
  infoText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    marginBottom: 4,
  },
});

export default EducationForm;