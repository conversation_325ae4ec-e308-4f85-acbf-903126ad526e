import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { useOnboarding } from '@/context/OnboardingContext';
import { X } from 'lucide-react-native';

const SkillsForm = () => {
  const { colors } = useTheme();
  const { profileData, updateProfileData } = useOnboarding();
  const [newSkill, setNewSkill] = useState('');

  const handleAddSkill = () => {
    if (newSkill.trim() === '') return;
    
    const updatedSkills = [...profileData.skills, newSkill];
    updateProfileData({ skills: updatedSkills });
    setNewSkill('');
  };

  const handleRemoveSkill = (index: number) => {
    const updatedSkills = [...profileData.skills];
    updatedSkills.splice(index, 1);
    updateProfileData({ skills: updatedSkills });
  };

  // Common skills that can be added quickly
  const commonSkills = [
    'JavaScript', 'React', 'Python', 'Java', 'SQL',
    'Project Management', 'Data Analysis', 'UX Design',
    'Leadership', 'Communication', 'Problem Solving'
  ];

  const addCommonSkill = (skill: string) => {
    if (profileData.skills.includes(skill)) return;
    
    const updatedSkills = [...profileData.skills, skill];
    updateProfileData({ skills: updatedSkills });
  };

  return (
    <View style={styles.container}>
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Your Skills</Text>
        <View style={styles.skillsContainer}>
          {profileData.skills.map((skill, index) => (
            <View key={index} style={[styles.skillTag, { backgroundColor: colors.primary[100] }]}>
              <Text style={[styles.skillTagText, { color: colors.primary[700] }]}>{skill}</Text>
              <TouchableOpacity onPress={() => handleRemoveSkill(index)}>
                <X size={16} color={colors.primary[700]} />
              </TouchableOpacity>
            </View>
          ))}
        </View>
        <View style={styles.skillInputContainer}>
          <TextInput
            style={[styles.skillInput, { 
              backgroundColor: colors.card, 
              borderColor: colors.border,
              color: colors.text 
            }]}
            value={newSkill}
            onChangeText={setNewSkill}
            placeholder="Add a skill"
            placeholderTextColor={colors.textLight}
            onSubmitEditing={handleAddSkill}
          />
          <TouchableOpacity 
            style={[styles.addButton, { backgroundColor: colors.primary[600] }]}
            onPress={handleAddSkill}
          >
            <Text style={[styles.addButtonText, { color: colors.white }]}>Add</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Common Skills</Text>
        <Text style={[styles.subLabel, { color: colors.textLight }]}>
          Tap to add these common skills to your profile
        </Text>
        <View style={styles.commonSkillsContainer}>
          {commonSkills.map((skill, index) => (
            <TouchableOpacity 
              key={index} 
              style={[
                styles.commonSkillTag, 
                { 
                  backgroundColor: profileData.skills.includes(skill) ? 
                    colors.primary[100] : colors.card,
                  borderColor: colors.border 
                }
              ]}
              onPress={() => addCommonSkill(skill)}
              disabled={profileData.skills.includes(skill)}
            >
              <Text 
                style={[
                  styles.commonSkillText, 
                  { 
                    color: profileData.skills.includes(skill) ? 
                      colors.primary[700] : colors.text 
                  }
                ]}
              >
                {skill}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.infoContainer}>
        <Text style={[styles.infoTitle, { color: colors.text }]}>Pro Tips</Text>
        <Text style={[styles.infoText, { color: colors.textLight }]}>
          • Add skills that are relevant to your target roles
        </Text>
        <Text style={[styles.infoText, { color: colors.textLight }]}>
          • Include both technical and soft skills
        </Text>
        <Text style={[styles.infoText, { color: colors.textLight }]}>
          • Be specific with technical skills (e.g. "React Native" instead of just "Mobile Development")
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
    marginBottom: 8,
  },
  subLabel: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    marginBottom: 12,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
    gap: 8,
  },
  skillTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    gap: 8,
  },
  skillTagText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  skillInputContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  skillInput: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  addButton: {
    paddingHorizontal: 16,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButtonText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  commonSkillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  commonSkillTag: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
  },
  commonSkillText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  infoContainer: {
    backgroundColor: '#F8FAFC',
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  infoTitle: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
    marginBottom: 8,
  },
  infoText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    marginBottom: 4,
  },
});

export default SkillsForm;