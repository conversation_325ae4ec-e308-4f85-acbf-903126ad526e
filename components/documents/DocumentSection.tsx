import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { ChevronRight, Plus } from 'lucide-react-native';
import { useTheme } from '@/hooks/useTheme';
import DocumentCard from './DocumentCard';

interface DocumentSectionProps {
  title: string;
  documents: any[];
  onAddDocument: () => void;
  onViewAll?: () => void;
}

const DocumentSection: React.FC<DocumentSectionProps> = ({
  title,
  documents,
  onAddDocument,
  onViewAll,
}) => {
  const { colors } = useTheme();
  const hasDocuments = documents && documents.length > 0;

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{title}</Text>
        {hasDocuments && (
          <TouchableOpacity style={styles.viewAllButton} onPress={onViewAll}>
            <Text style={[styles.viewAllText, { color: colors.primary[600] }]}>View All</Text>
            <ChevronRight size={16} color={colors.primary[600]} />
          </TouchableOpacity>
        )}
      </View>

      {hasDocuments ? (
        <View>
          {documents.slice(0, 2).map((doc, index) => (
            <DocumentCard
              key={index}
              title={doc.title}
              fileName={doc.fileName}
              fileSize={doc.fileSize}
              uploadDate={doc.uploadDate}
              type={doc.type}
              onView={doc.onView}
              onDelete={doc.onDelete}
            />
          ))}
        </View>
      ) : (
        <View style={[styles.emptyContainer, { backgroundColor: colors.background, borderColor: colors.border }]}>
          <Text style={[styles.emptyText, { color: colors.textLight }]}>
            No documents uploaded yet
          </Text>
        </View>
      )}

      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: colors.primary[50] }]}
        onPress={onAddDocument}
        activeOpacity={0.7}
      >
        <Plus size={20} color={colors.primary[600]} />
        <Text style={[styles.addButtonText, { color: colors.primary[600] }]}>
          Add {title}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 18,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  emptyContainer: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    height: 100,
  },
  emptyText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  addButtonText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
});

export default DocumentSection;