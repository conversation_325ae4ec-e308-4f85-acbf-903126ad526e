import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { File, Download, Trash2 } from 'lucide-react-native';
import { useTheme } from '@/hooks/useTheme';

interface DocumentCardProps {
  title: string;
  fileName: string;
  fileSize: string;
  uploadDate: string;
  type: 'resume' | 'certificate' | 'id' | 'letter' | 'other';
  onView?: () => void;
  onDelete?: () => void;
}

const DocumentCard: React.FC<DocumentCardProps> = ({
  title,
  fileName,
  fileSize,
  uploadDate,
  type,
  onView,
  onDelete,
}) => {
  const { colors } = useTheme();

  const getIconColor = () => {
    switch (type) {
      case 'resume':
        return colors.primary[600];
      case 'certificate':
        return '#F59E0B'; // Amber
      case 'id':
        return '#10B981'; // Emerald
      case 'letter':
        return '#8B5CF6'; // Violet
      case 'other':
      default:
        return '#6B7280'; // Gray
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <View style={styles.leftSection}>
        <View style={[styles.iconContainer, { backgroundColor: `${getIconColor()}20` }]}>
          <File size={24} color={getIconColor()} />
        </View>
        <View style={styles.infoContainer}>
          <Text style={[styles.title, { color: colors.text }]} numberOfLines={1}>
            {title}
          </Text>
          <Text style={[styles.fileName, { color: colors.textLight }]} numberOfLines={1}>
            {fileName}
          </Text>
          <View style={styles.metaContainer}>
            <Text style={[styles.metaText, { color: colors.textLight }]}>
              {fileSize} • {uploadDate}
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.actions}>
        <TouchableOpacity 
          style={[styles.actionButton, { backgroundColor: colors.primary[50] }]} 
          onPress={onView}
          activeOpacity={0.7}
        >
          <Download size={20} color={colors.primary[600]} />
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.actionButton, { backgroundColor: '#FEE2E2' }]} 
          onPress={onDelete}
          activeOpacity={0.7}
        >
          <Trash2 size={20} color='#EF4444' />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  infoContainer: {
    flex: 1,
  },
  title: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
  },
  fileName: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  metaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  metaText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default DocumentCard;