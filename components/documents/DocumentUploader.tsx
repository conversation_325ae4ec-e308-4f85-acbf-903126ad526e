import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import { X, Upload, FileText } from 'lucide-react-native';
import { useTheme } from '@/hooks/useTheme';

interface DocumentUploaderProps {
  visible: boolean;
  onClose: () => void;
  documentType: string;
  onUpload: (data: any) => void;
}

const DocumentUploader: React.FC<DocumentUploaderProps> = ({
  visible,
  onClose,
  documentType,
  onUpload,
}) => {
  const { colors } = useTheme();
  const [uploading, setUploading] = useState(false);

  const handleUpload = async () => {
    setUploading(true);
    // Simulate upload
    setTimeout(() => {
      setUploading(false);
      onUpload({
        title: `${documentType} Document`,
        fileName: 'document.pdf',
        fileSize: '2.4 MB',
        uploadDate: 'Today',
        type: documentType.toLowerCase() as any,
      });
      onClose();
    }, 1500);
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContainer, { backgroundColor: colors.card }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Upload {documentType}
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.modalContent}>
            <TouchableOpacity
              style={[styles.uploadArea, { borderColor: colors.border }]}
              onPress={handleUpload}
              activeOpacity={0.7}
              disabled={uploading}
            >
              <View style={[styles.uploadIconContainer, { backgroundColor: colors.primary[50] }]}>
                {uploading ? (
                  <Text style={{ color: colors.primary[600] }}>Uploading...</Text>
                ) : (
                  <>
                    <FileText size={32} color={colors.primary[600]} />
                    <Text style={[styles.uploadText, { color: colors.primary[600] }]}>
                      Tap to select document
                    </Text>
                    <Text style={[styles.uploadSubtext, { color: colors.textLight }]}>
                      PDF, DOCX, JPG or PNG (Max. 10MB)
                    </Text>
                  </>
                )}
              </View>
            </TouchableOpacity>
          </View>
          
          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton, { borderColor: colors.border }]}
              onPress={onClose}
              disabled={uploading}
            >
              <Text style={[styles.buttonText, { color: colors.text }]}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.button,
                styles.uploadButton,
                { backgroundColor: colors.primary[600] },
                uploading && { opacity: 0.7 }
              ]}
              onPress={handleUpload}
              disabled={uploading}
            >
              <Upload size={20} color={colors.white} />
              <Text style={[styles.buttonText, { color: colors.white }]}>
                {uploading ? 'Uploading...' : 'Upload'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    minHeight: '60%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 20,
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 20,
  },
  uploadArea: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  uploadIconContainer: {
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  uploadText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
    marginTop: 12,
    textAlign: 'center',
  },
  uploadSubtext: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    gap: 12,
  },
  button: {
    paddingVertical: 12,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    gap: 8,
  },
  cancelButton: {
    borderWidth: 1,
  },
  uploadButton: {
    paddingVertical: 12,
  },
  buttonText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
  },
});

export default DocumentUploader;