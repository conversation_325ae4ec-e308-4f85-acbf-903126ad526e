import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { Calendar, Hash, AlertCircle } from 'lucide-react-native';

interface CertificationCardProps {
  title: string;
  issuer: string;
  date: string;
  expiryDate?: string;
  credentialId: string;
}

export default function CertificationCard({
  title,
  issuer,
  date,
  expiryDate,
  credentialId,
}: CertificationCardProps) {
  const { colors } = useTheme();

  const isExpired = expiryDate && new Date(expiryDate) < new Date();
  const isExpiringSoon = expiryDate && !isExpired && 
    new Date(expiryDate).getTime() - new Date().getTime() < 90 * 24 * 60 * 60 * 1000; // 90 days

  return (
    <View style={[styles.container, { backgroundColor: colors.white }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
        <Text style={[styles.issuer, { color: colors.primary[600] }]}>{issuer}</Text>
      </View>
      
      <View style={styles.details}>
        <View style={styles.detailRow}>
          <Calendar size={14} color={colors.textLight} />
          <Text style={[styles.detailText, { color: colors.textLight }]}>
            Issued: {date}
          </Text>
        </View>
        
        {expiryDate && (
          <View style={styles.detailRow}>
            <AlertCircle 
              size={14} 
              color={isExpired ? colors.error : isExpiringSoon ? colors.warning : colors.textLight} 
            />
            <Text style={[
              styles.detailText, 
              { 
                color: isExpired ? colors.error : isExpiringSoon ? colors.warning : colors.textLight 
              }
            ]}>
              Expires: {expiryDate}
            </Text>
          </View>
        )}
        
        <View style={styles.detailRow}>
          <Hash size={14} color={colors.textLight} />
          <Text style={[styles.detailText, { color: colors.textLight }]}>
            ID: {credentialId}
          </Text>
        </View>
      </View>
      
      {isExpired && (
        <View style={[styles.statusBadge, { backgroundColor: colors.error + '20' }]}>
          <Text style={[styles.statusText, { color: colors.error }]}>Expired</Text>
        </View>
      )}
      
      {isExpiringSoon && !isExpired && (
        <View style={[styles.statusBadge, { backgroundColor: colors.warning + '20' }]}>
          <Text style={[styles.statusText, { color: colors.warning }]}>Expiring Soon</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    marginBottom: 12,
  },
  title: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    marginBottom: 4,
  },
  issuer: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  details: {
    gap: 6,
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  detailText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginTop: 8,
  },
  statusText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 11,
  },
});
