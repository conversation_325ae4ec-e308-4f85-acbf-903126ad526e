import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { MapPin, Calendar, Award } from 'lucide-react-native';

interface EducationCardProps {
  degree: string;
  institution: string;
  location: string;
  year: string;
  gpa?: string;
}

export default function EducationCard({
  degree,
  institution,
  location,
  year,
  gpa,
}: EducationCardProps) {
  const { colors } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: colors.white }]}>
      <View style={styles.header}>
        <Text style={[styles.degree, { color: colors.text }]}>{degree}</Text>
        <Text style={[styles.institution, { color: colors.primary[600] }]}>{institution}</Text>
      </View>
      
      <View style={styles.details}>
        <View style={styles.detailRow}>
          <MapPin size={14} color={colors.textLight} />
          <Text style={[styles.detailText, { color: colors.textLight }]}>{location}</Text>
        </View>
        <View style={styles.detailRow}>
          <Calendar size={14} color={colors.textLight} />
          <Text style={[styles.detailText, { color: colors.textLight }]}>{year}</Text>
        </View>
        {gpa && (
          <View style={styles.detailRow}>
            <Award size={14} color={colors.textLight} />
            <Text style={[styles.detailText, { color: colors.textLight }]}>GPA: {gpa}</Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    marginBottom: 12,
  },
  degree: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    marginBottom: 4,
  },
  institution: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  details: {
    gap: 6,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  detailText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
  },
});
