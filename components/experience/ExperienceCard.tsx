import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { MapPin, Calendar } from 'lucide-react-native';

interface ExperienceCardProps {
  title: string;
  company: string;
  location: string;
  startDate: string;
  endDate: string;
  description: string;
}

export default function ExperienceCard({
  title,
  company,
  location,
  startDate,
  endDate,
  description,
}: ExperienceCardProps) {
  const { colors } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: colors.white }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
        <Text style={[styles.company, { color: colors.primary[600] }]}>{company}</Text>
      </View>
      
      <View style={styles.details}>
        <View style={styles.detailRow}>
          <MapPin size={14} color={colors.textLight} />
          <Text style={[styles.detailText, { color: colors.textLight }]}>{location}</Text>
        </View>
        <View style={styles.detailRow}>
          <Calendar size={14} color={colors.textLight} />
          <Text style={[styles.detailText, { color: colors.textLight }]}>
            {startDate} - {endDate}
          </Text>
        </View>
      </View>
      
      <Text style={[styles.description, { color: colors.text }]}>{description}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    marginBottom: 12,
  },
  title: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    marginBottom: 4,
  },
  company: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  details: {
    gap: 6,
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  detailText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
  },
  description: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    lineHeight: 20,
  },
});
