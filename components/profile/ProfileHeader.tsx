import { View, Text, StyleSheet, Image } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { UserCircle2 } from 'lucide-react-native';

interface ProfileHeaderProps {
  name: string;
  title: string;
  photoUri?: string;
}

export default function ProfileHeader({ name, title, photoUri }: ProfileHeaderProps) {
  const { colors } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: colors.primary[600] }]}>
      <View style={styles.content}>
        <View style={styles.avatarContainer}>
          {photoUri ? (
            <Image source={{ uri: photoUri }} style={styles.avatar} />
          ) : (
            <View style={[styles.avatarPlaceholder, { backgroundColor: colors.primary[700] }]}>
              <UserCircle2 size={60} color={colors.white} />
            </View>
          )}
        </View>
        
        <View style={styles.textContainer}>
          <Text style={[styles.name, { color: colors.white }]}>{name}</Text>
          <Text style={[styles.title, { color: colors.primary[100] }]}>{title}</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 16,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    overflow: 'hidden',
  },
  avatar: {
    width: '100%',
    height: '100%',
  },
  avatarPlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 40,
  },
  textContainer: {
    flex: 1,
  },
  name: {
    fontFamily: 'Poppins-Bold',
    fontSize: 24,
    marginBottom: 4,
  },
  title: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
  },
});
