import { View, Text, StyleSheet, TouchableOpacity, ReactNode } from 'react-native';
import { useTheme } from '@/hooks/useTheme';
import { ChevronRight } from 'lucide-react-native';

interface ProfileSectionProps {
  title: string;
  icon: ReactNode;
  children: ReactNode;
  onPress?: () => void;
}

export default function ProfileSection({ title, icon, children, onPress }: ProfileSectionProps) {
  const { colors } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: colors.white }]}>
      <TouchableOpacity 
        style={styles.header}
        onPress={onPress}
        disabled={!onPress}
        activeOpacity={onPress ? 0.7 : 1}
      >
        <View style={styles.headerLeft}>
          {icon}
          <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
        </View>
        {onPress && (
          <ChevronRight size={20} color={colors.textLight} />
        )}
      </TouchableOpacity>
      
      <View style={styles.content}>
        {children}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  title: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
  },
  content: {
    padding: 16,
  },
});
