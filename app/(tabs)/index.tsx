import { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { useOnboarding } from '@/context/OnboardingContext';
import ProfileHeader from '@/components/profile/ProfileHeader';
import ProfileSection from '@/components/profile/ProfileSection';
import { Edit, MapPin, Briefcase, GraduationCap, Award } from 'lucide-react-native';
import { router } from 'expo-router';

export default function ProfileScreen() {
  const { colors } = useTheme();
  const { profileData } = useOnboarding();
  const [completionPercent, setCompletionPercent] = useState(65);

  const handleEditProfile = () => {
    router.push('/onboarding');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <ProfileHeader 
          name={profileData.fullName || 'Your Name'}
          title={profileData.jobTitle || 'Professional Title'}
          photoUri={profileData.photoUri}
        />

        <View style={styles.completionContainer}>
          <View style={styles.completionTextContainer}>
            <Text style={[styles.completionTitle, { color: colors.text }]}>Profile Completion</Text>
            <Text style={[styles.completionPercent, { color: colors.primary[600] }]}>{completionPercent}%</Text>
          </View>
          <View style={[styles.progressBg, { backgroundColor: colors.border }]}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  backgroundColor: colors.primary[600],
                  width: `${completionPercent}%`
                }
              ]} 
            />
          </View>
        </View>

        <ProfileSection 
          title="Personal Information"
          icon={<Edit size={20} color={colors.primary[600]} />}
          onPress={() => {}}
        >
          <View style={styles.infoRow}>
            <MapPin size={16} color={colors.textLight} />
            <Text style={[styles.infoText, { color: colors.text }]}>
              {profileData.location || 'Location not specified'}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={[styles.infoText, { color: colors.text }]}>
              {profileData.email || 'Email not provided'}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={[styles.infoText, { color: colors.text }]}>
              {profileData.phone || 'Phone not provided'}
            </Text>
          </View>
        </ProfileSection>

        <ProfileSection 
          title="Job Preferences"
          icon={<Briefcase size={20} color={colors.primary[600]} />}
          onPress={() => {}}
        >
          <Text style={[styles.preferencesText, { color: colors.text }]}>
            {profileData.jobType || 'Full-time'} • {profileData.jobLocation || 'Remote'}
          </Text>
          <Text style={[styles.preferencesText, { color: colors.text }]}>
            Expected Salary: {profileData.expectedSalary || 'Not specified'}
          </Text>
          <View style={styles.tagContainer}>
            {(profileData.preferredCountries || ['United States', 'Canada', 'United Kingdom']).map((country, index) => (
              <View key={index} style={[styles.tag, { backgroundColor: colors.primary[100] }]}>
                <Text style={[styles.tagText, { color: colors.primary[700] }]}>{country}</Text>
              </View>
            ))}
          </View>
        </ProfileSection>

        <ProfileSection 
          title="Education"
          icon={<GraduationCap size={20} color={colors.primary[600]} />}
          onPress={() => {}}
        >
          <View style={styles.educationItem}>
            <Text style={[styles.degreeText, { color: colors.text }]}>
              {profileData.degree || 'Bachelor of Science in Computer Science'}
            </Text>
            <Text style={[styles.institutionText, { color: colors.textLight }]}>
              {profileData.institution || 'University of Technology'} • {profileData.graduationYear || '2020'}
            </Text>
          </View>
        </ProfileSection>

        <ProfileSection 
          title="Skills"
          icon={<Award size={20} color={colors.primary[600]} />}
          onPress={() => {}}
        >
          <View style={styles.skillsContainer}>
            {(profileData.skills || ['JavaScript', 'React Native', 'UI/UX Design', 'Node.js', 'Git']).map((skill, index) => (
              <View key={index} style={[styles.skillTag, { backgroundColor: colors.primary[100] }]}>
                <Text style={[styles.skillTagText, { color: colors.primary[700] }]}>{skill}</Text>
              </View>
            ))}
          </View>
        </ProfileSection>

        <TouchableOpacity 
          style={[styles.editButton, { backgroundColor: colors.primary[600] }]}
          onPress={handleEditProfile}
        >
          <Edit size={20} color={colors.white} />
          <Text style={[styles.editButtonText, { color: colors.white }]}>Edit Profile</Text>
        </TouchableOpacity>

        <View style={{ height: 40 }} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  completionContainer: {
    marginHorizontal: 16,
    marginVertical: 20,
  },
  completionTextContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  completionTitle: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
  },
  completionPercent: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
  },
  progressBg: {
    height: 8,
    borderRadius: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
    gap: 8,
  },
  infoText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  preferencesText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    marginBottom: 8,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    gap: 8,
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 12,
  },
  educationItem: {
    marginVertical: 4,
  },
  degreeText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  institutionText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  skillTag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  skillTagText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 12,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
    marginTop: 24,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  editButtonText: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
  }
});