import { useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import ScreenHeader from '@/components/ui/ScreenHeader';
import DocumentSection from '@/components/documents/DocumentSection';
import DocumentUploader from '@/components/documents/DocumentUploader';

export default function DocumentsScreen() {
  const { colors } = useTheme();
  const [uploaderVisible, setUploaderVisible] = useState(false);
  const [currentDocumentType, setCurrentDocumentType] = useState('');
  const [documents, setDocuments] = useState({
    resumes: [],
    certificates: [],
    identifications: [],
    letters: [],
    others: []
  });

  const handleAddDocument = (type: string) => {
    setCurrentDocumentType(type);
    setUploaderVisible(true);
  };

  const handleUploadDocument = (doc: any) => {
    switch (currentDocumentType.toLowerCase()) {
      case 'resume':
        setDocuments({
          ...documents,
          resumes: [...documents.resumes, doc]
        });
        break;
      case 'certificate':
        setDocuments({
          ...documents,
          certificates: [...documents.certificates, doc]
        });
        break;
      case 'identification':
        setDocuments({
          ...documents,
          identifications: [...documents.identifications, doc]
        });
        break;
      case 'experience letter':
        setDocuments({
          ...documents,
          letters: [...documents.letters, doc]
        });
        break;
      case 'other':
        setDocuments({
          ...documents,
          others: [...documents.others, doc]
        });
        break;
    }
  };

  const deleteDocument = (type: string, index: number) => {
    const newDocuments = { ...documents };
    (newDocuments as any)[type].splice(index, 1);
    setDocuments(newDocuments);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
      <ScreenHeader title="Documents & Credentials" />

      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.infoContainer}>
          <Text style={[styles.infoText, { color: colors.textLight }]}>
            Upload your professional documents for verification and easy access during job applications.
          </Text>
        </View>

        <DocumentSection
          title="Resume/CV"
          documents={documents.resumes}
          onAddDocument={() => handleAddDocument('Resume')}
        />

        <DocumentSection
          title="Certificates"
          documents={documents.certificates}
          onAddDocument={() => handleAddDocument('Certificate')}
        />

        <DocumentSection
          title="Identifications"
          documents={documents.identifications}
          onAddDocument={() => handleAddDocument('Identification')}
        />

        <DocumentSection
          title="Experience Letters"
          documents={documents.letters}
          onAddDocument={() => handleAddDocument('Experience Letter')}
        />

        <DocumentSection
          title="Other Documents"
          documents={documents.others}
          onAddDocument={() => handleAddDocument('Other')}
        />

        <View style={{ height: 40 }} />
      </ScrollView>

      <DocumentUploader
        visible={uploaderVisible}
        onClose={() => setUploaderVisible(false)}
        documentType={currentDocumentType}
        onUpload={handleUploadDocument}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  infoContainer: {
    marginBottom: 24,
  },
  infoText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    lineHeight: 22,
  },
});