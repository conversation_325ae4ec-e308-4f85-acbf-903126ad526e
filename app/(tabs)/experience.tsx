import { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import ScreenHeader from '@/components/ui/ScreenHeader';
import { Briefcase, GraduationCap, Award, Plus } from 'lucide-react-native';
import ExperienceCard from '@/components/experience/ExperienceCard';
import EducationCard from '@/components/experience/EducationCard';
import CertificationCard from '@/components/experience/CertificationCard';

export default function ExperienceScreen() {
  const { colors } = useTheme();
  const [activeTab, setActiveTab] = useState('work');

  const renderContent = () => {
    switch (activeTab) {
      case 'work':
        return (
          <>
            <ExperienceCard 
              title="Senior Mobile Developer"
              company="Tech Innovations Inc."
              location="San Francisco, CA"
              startDate="Jan 2021"
              endDate="Present"
              description="Led development of mobile applications using React Native and integrated with backend APIs. Implemented CI/CD pipelines and mentored junior developers."
            />
            <ExperienceCard 
              title="Mobile Developer"
              company="Digital Solutions Ltd."
              location="New York, NY"
              startDate="Mar 2018"
              endDate="Dec 2020"
              description="Developed cross-platform mobile applications for iOS and Android using React Native framework. Collaborated with design and backend teams to implement features."
            />
            <TouchableOpacity 
              style={[styles.addButton, { backgroundColor: colors.primary[100] }]}
              activeOpacity={0.8}
            >
              <Plus size={20} color={colors.primary[600]} />
              <Text style={[styles.addButtonText, { color: colors.primary[600] }]}>Add Work Experience</Text>
            </TouchableOpacity>
          </>
        );
      case 'education':
        return (
          <>
            <EducationCard 
              degree="Master of Computer Science"
              institution="University of Technology"
              location="Boston, MA"
              year="2018"
              gpa="3.8/4.0"
            />
            <EducationCard 
              degree="Bachelor of Engineering"
              institution="State University"
              location="Chicago, IL"
              year="2016"
              gpa="3.6/4.0"
            />
            <TouchableOpacity 
              style={[styles.addButton, { backgroundColor: colors.primary[100] }]}
              activeOpacity={0.8}
            >
              <Plus size={20} color={colors.primary[600]} />
              <Text style={[styles.addButtonText, { color: colors.primary[600] }]}>Add Education</Text>
            </TouchableOpacity>
          </>
        );
      case 'certification':
        return (
          <>
            <CertificationCard 
              title="AWS Certified Developer"
              issuer="Amazon Web Services"
              date="May 2022"
              expiryDate="May 2025"
              credentialId="AWS-DEV-123456"
            />
            <CertificationCard 
              title="React Native Professional"
              issuer="Meta"
              date="Jan 2021"
              credentialId="RN-PROF-789012"
            />
            <TouchableOpacity 
              style={[styles.addButton, { backgroundColor: colors.primary[100] }]}
              activeOpacity={0.8}
            >
              <Plus size={20} color={colors.primary[600]} />
              <Text style={[styles.addButtonText, { color: colors.primary[600] }]}>Add Certification</Text>
            </TouchableOpacity>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
      <ScreenHeader title="Experience & Qualifications" />
      
      <View style={styles.tabContainer}>
        <TouchableOpacity 
          style={[
            styles.tab, 
            activeTab === 'work' && [styles.activeTab, { borderBottomColor: colors.primary[600] }]
          ]}
          onPress={() => setActiveTab('work')}
        >
          <Briefcase 
            size={20} 
            color={activeTab === 'work' ? colors.primary[600] : colors.textLight} 
          />
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === 'work' ? colors.primary[600] : colors.textLight }
            ]}
          >
            Work
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.tab, 
            activeTab === 'education' && [styles.activeTab, { borderBottomColor: colors.primary[600] }]
          ]}
          onPress={() => setActiveTab('education')}
        >
          <GraduationCap 
            size={20} 
            color={activeTab === 'education' ? colors.primary[600] : colors.textLight} 
          />
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === 'education' ? colors.primary[600] : colors.textLight }
            ]}
          >
            Education
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.tab, 
            activeTab === 'certification' && [styles.activeTab, { borderBottomColor: colors.primary[600] }]
          ]}
          onPress={() => setActiveTab('certification')}
        >
          <Award 
            size={20} 
            color={activeTab === 'certification' ? colors.primary[600] : colors.textLight} 
          />
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === 'certification' ? colors.primary[600] : colors.textLight }
            ]}
          >
            Certifications
          </Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {renderContent()}
        <View style={{ height: 40 }} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 8,
    flex: 1,
    justifyContent: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    gap: 16,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    marginTop: 8,
    gap: 8,
  },
  addButtonText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
});