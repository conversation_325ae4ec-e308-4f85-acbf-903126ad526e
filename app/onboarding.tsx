import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useState, useRef, useEffect } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { router } from 'expo-router';
import { ArrowLeft, ArrowRight, Check } from 'lucide-react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming,
  interpolate,
  Extrapolate
} from 'react-native-reanimated';
import { useOnboarding } from '@/context/OnboardingContext';
import PersonalInfoForm from '@/components/onboarding/PersonalInfoForm';
import JobPreferencesForm from '@/components/onboarding/JobPreferencesForm';
import EducationForm from '@/components/onboarding/EducationForm';
import SkillsForm from '@/components/onboarding/SkillsForm';

const STEPS = [
  { id: 'personal', title: 'Personal Info', component: PersonalInfoForm },
  { id: 'job', title: 'Job Preferences', component: JobPreferencesForm },
  { id: 'education', title: 'Education', component: EducationForm },
  { id: 'skills', title: 'Skills & Tools', component: SkillsForm },
];

export default function OnboardingScreen() {
  const { colors } = useTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const scrollRef = useRef<ScrollView>(null);
  const progress = useSharedValue(0);
  const { updateProfileData, profileData } = useOnboarding();

  useEffect(() => {
    progress.value = withTiming(currentStep / (STEPS.length - 1), { duration: 300 });
  }, [currentStep]);

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: `${interpolate(progress.value, [0, 1], [5, 100], Extrapolate.CLAMP)}%`,
    };
  });

  const handleNext = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
      scrollRef.current?.scrollTo({ y: 0, animated: true });
    } else {
      // Complete onboarding
      router.push('/(tabs)');
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      scrollRef.current?.scrollTo({ y: 0, animated: true });
    } else {
      router.back();
    }
  };

  const handleSkip = () => {
    router.push('/(tabs)');
  };

  const CurrentStepComponent = STEPS[currentStep].component;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <View style={styles.titleContainer}>
          <Text style={[styles.stepText, { color: colors.textLight }]}>
            Step {currentStep + 1} of {STEPS.length}
          </Text>
          <Text style={[styles.title, { color: colors.text }]}>
            {STEPS[currentStep].title}
          </Text>
        </View>
        <TouchableOpacity onPress={handleSkip} style={styles.skipButton}>
          <Text style={[styles.skipText, { color: colors.primary[500] }]}>Skip</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.progressContainer}>
        <Animated.View 
          style={[
            styles.progressBar, 
            progressStyle, 
            { backgroundColor: colors.primary[600] }
          ]} 
        />
      </View>

      <ScrollView 
        ref={scrollRef}
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <CurrentStepComponent />
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity 
          style={[styles.button, { backgroundColor: colors.primary[600] }]}
          onPress={handleNext}
        >
          <Text style={[styles.buttonText, { color: colors.white }]}>
            {currentStep === STEPS.length - 1 ? 'Complete' : 'Continue'}
          </Text>
          {currentStep === STEPS.length - 1 ? (
            <Check size={20} color={colors.white} />
          ) : (
            <ArrowRight size={20} color={colors.white} />
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
  },
  titleContainer: {
    alignItems: 'center',
  },
  title: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 18,
  },
  stepText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    marginBottom: 4,
  },
  skipButton: {
    padding: 8,
  },
  skipText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
  },
  progressContainer: {
    height: 4,
    backgroundColor: '#E0E0E0',
    width: '100%',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  buttonText: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
  },
});