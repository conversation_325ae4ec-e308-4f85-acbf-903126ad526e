import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/useTheme';
import { UserCircle2 } from 'lucide-react-native';

export default function WelcomeScreen() {
  const { colors } = useTheme();

  const handleGetStarted = () => {
    router.push('/onboarding');
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[colors.primary[800], colors.primary[600]]}
        style={styles.gradient}
      >
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.content}>
            <View style={styles.logoContainer}>
              <UserCircle2 size={80} color={colors.white} />
              <Text style={[styles.appName, { color: colors.white }]}>ProFile</Text>
            </View>
            
            <View style={styles.heroContainer}>
              <Text style={[styles.title, { color: colors.white }]}>
                Your Professional Profile, Globally Ready
              </Text>
              <Text style={[styles.subtitle, { color: colors.white }]}>
                Create a comprehensive professional profile to showcase your talents
                to recruiters worldwide.
              </Text>
            </View>

            <TouchableOpacity 
              style={[styles.button, { backgroundColor: colors.white }]}
              onPress={handleGetStarted}
              activeOpacity={0.8}
            >
              <Text style={[styles.buttonText, { color: colors.primary[700] }]}>
                Get Started
              </Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    paddingVertical: 60,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
  appName: {
    fontFamily: 'Poppins-Bold',
    fontSize: 28,
    marginTop: 16,
  },
  heroContainer: {
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  title: {
    fontFamily: 'Poppins-Bold',
    fontSize: 28,
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    fontFamily: 'Poppins-Regular',
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  button: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
    marginHorizontal: 24,
    marginBottom: 20,
  },
  buttonText: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 18,
  },
});