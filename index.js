// Custom entry point for JSC compatibility
// Load polyfills first to prevent Hermes-related errors

// Aggressive Hermes error prevention - must run before any imports
if (typeof global !== 'undefined') {
  // Override require at the earliest possible moment
  const originalRequire = global.require;
  global.require = function(moduleName) {
    // Block all Hermes-related modules
    if (typeof moduleName === 'string' && (
      moduleName.includes('hermes') ||
      moduleName.includes('Hermes') ||
      moduleName.includes('InitializeCore') ||
      moduleName.includes('react-native/Libraries/Core/InitializeCore')
    )) {
      // Return a mock object instead of throwing
      return {
        __esModule: true,
        default: {},
      };
    }

    // Try original require, catch and suppress Hermes errors
    if (originalRequire) {
      try {
        return originalRequire(moduleName);
      } catch (error) {
        if (error.message && (
          error.message.includes('hermes') ||
          error.message.includes('Hermes') ||
          error.message.includes("Property 'require' doesn't exist")
        )) {
          return {
            __esModule: true,
            default: {},
          };
        }
        throw error;
      }
    }

    return {};
  };

  // Force disable Hermes detection
  global.__HERMES__ = false;
  global.__JSC__ = true;

  // Suppress console errors related to Hermes
  const originalError = console.error;
  console.error = function(...args) {
    const message = args[0];
    if (typeof message === 'string' && (
      message.includes('hermes') ||
      message.includes('Hermes') ||
      message.includes("Property 'require' doesn't exist")
    )) {
      return; // Suppress the error
    }
    return originalError.apply(console, args);
  };
}

// Import polyfills before anything else
import './polyfills';

// Import the main Expo Router entry point
import 'expo-router/entry';
