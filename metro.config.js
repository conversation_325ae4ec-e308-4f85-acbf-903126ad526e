const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Disable source maps to avoid the 'unknown' file error
config.serializer.createModuleIdFactory = function () {
  return function (path) {
    return path;
  };
};

// Disable symbolication to prevent the unknown file error
config.symbolicator = {
  customizeFrame: () => null,
};

module.exports = config;
