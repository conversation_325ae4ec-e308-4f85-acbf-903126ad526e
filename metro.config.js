const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add polyfills to the beginning of the bundle
config.serializer.getPolyfills = () => [
  path.resolve(__dirname, 'polyfills.js'),
];

// Configure for better JSC compatibility (disable Hermes features)
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Block Hermes-related modules at the resolver level
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];
config.resolver.blacklistRE = /.*hermes.*/i;

// Custom resolver to handle Hermes-specific modules
const originalResolver = config.resolver.resolverMainFields;
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Block specific Hermes modules
  if (moduleName === './parseHermesStack' ||
      moduleName.includes('parseHermesStack') ||
      moduleName.includes('hermes') ||
      moduleName.includes('Hermes')) {
    return {
      filePath: path.resolve(__dirname, 'polyfills.js'),
      type: 'sourceFile',
    };
  }

  // Use default resolver for other modules
  return context.resolveRequest(context, moduleName, platform);
};

// Disable source maps to avoid the 'unknown' file error
config.serializer.createModuleIdFactory = function () {
  return function (path) {
    return path;
  };
};

// Completely disable symbolication to prevent Hermes-related errors
config.symbolicator = {
  customizeFrame: () => null,
};

// Add transformer options for JSC compatibility
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    mangle: {
      keep_fnames: true,
    },
  },
  // Disable Hermes-specific transformations
  hermesParser: false,
  enableBabelRCLookup: false,
};

// Disable Metro's built-in symbolication
config.server = {
  ...config.server,
  enhanceMiddleware: (middleware) => {
    return (req, res, next) => {
      if (req.url && req.url.includes('symbolicate')) {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end('{}');
        return;
      }
      return middleware(req, res, next);
    };
  },
};

module.exports = config;
