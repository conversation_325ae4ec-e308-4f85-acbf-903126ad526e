const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Configure for better Hermes compatibility
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Disable source maps to avoid the 'unknown' file error
config.serializer.createModuleIdFactory = function () {
  return function (path) {
    return path;
  };
};

// Disable symbolication to prevent the unknown file error
config.symbolicator = {
  customizeFrame: () => null,
};

// Add transformer options for better compatibility
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    mangle: {
      keep_fnames: true,
    },
  },
};

module.exports = config;
