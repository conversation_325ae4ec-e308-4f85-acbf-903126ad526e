// Polyfills for JSC compatibility and Hermes error prevention
// This file provides compatibility shims for React Native without Hermes

// Global polyfills
if (typeof global === 'undefined') {
  global = globalThis || window || this;
}

// Force JSC mode and disable Her<PERSON> detection
global.__HERMES__ = false;
global.__JSC__ = true;

// Ensure require function exists and is properly defined
if (typeof global.require === 'undefined') {
  global.require = function(moduleName) {
    // Suppress Hermes-related modules
    if (moduleName && typeof moduleName === 'string') {
      if (moduleName.includes('hermes') || moduleName.includes('Hermes')) {
        return {};
      }
    }

    // For development, we'll use a simple module resolution
    if (typeof __DEV__ !== 'undefined' && __DEV__) {
      console.warn(`Polyfill: require('${moduleName}') called`);
    }

    // Return empty object for unknown modules to prevent crashes
    return {};
  };
}

// Override any existing require to prevent Hermes module loading
const originalRequire = global.require;
global.require = function(moduleName) {
  // Block Hermes-specific modules
  if (moduleName && typeof moduleName === 'string') {
    if (moduleName.includes('hermes') ||
        moduleName.includes('Hermes') ||
        moduleName.includes('InitializeCore')) {
      return {};
    }
  }

  // Use original require if available, otherwise use our polyfill
  if (originalRequire && typeof originalRequire === 'function') {
    try {
      return originalRequire(moduleName);
    } catch (error) {
      // Suppress Hermes-related errors
      if (error.message && error.message.includes('hermes')) {
        return {};
      }
      throw error;
    }
  }

  return {};
};

// Polyfill for Hermes-specific globals that might be referenced
if (typeof global.HermesInternal === 'undefined') {
  global.HermesInternal = {
    hasPromise: () => true,
    enqueueJob: (callback) => {
      if (typeof setImmediate !== 'undefined') {
        setImmediate(callback);
      } else {
        setTimeout(callback, 0);
      }
    },
    getEpilogues: () => [],
    setEpilogues: () => {},
    getInstrumentedStats: () => ({}),
    getRuntimeProperties: () => ({}),
    ttiReached: () => {},
    getFBLogs: () => [],
    setFBLogs: () => {},
  };
}

// Polyfill for React Native specific globals
if (typeof global.__DEV__ === 'undefined') {
  global.__DEV__ = typeof __DEV__ !== 'undefined' ? __DEV__ : true;
}

// Polyfill for Metro bundler globals
if (typeof global.__METRO_GLOBAL_PREFIX__ === 'undefined') {
  global.__METRO_GLOBAL_PREFIX__ = '';
}

// Polyfill for React Native bridge
if (typeof global.__fbBatchedBridge === 'undefined') {
  global.__fbBatchedBridge = {
    callFunctionReturnFlushedQueue: () => null,
    invokeCallbackAndReturnFlushedQueue: () => null,
    flushedQueue: () => null,
    getEventLoopRunningTime: () => 0,
  };
}

// Polyfill for performance API
if (typeof global.performance === 'undefined') {
  global.performance = {
    now: () => Date.now(),
    mark: () => {},
    measure: () => {},
    getEntriesByName: () => [],
    getEntriesByType: () => [],
    clearMarks: () => {},
    clearMeasures: () => {},
  };
}

// Polyfill for console methods that might be missing
if (typeof global.console === 'undefined') {
  global.console = {
    log: () => {},
    warn: () => {},
    error: () => {},
    info: () => {},
    debug: () => {},
    trace: () => {},
    group: () => {},
    groupEnd: () => {},
    time: () => {},
    timeEnd: () => {},
  };
}

// Polyfill for Promise if not available
if (typeof global.Promise === 'undefined') {
  // Simple Promise polyfill for basic compatibility
  global.Promise = class Promise {
    constructor(executor) {
      this.state = 'pending';
      this.value = undefined;
      this.handlers = [];

      const resolve = (value) => {
        if (this.state === 'pending') {
          this.state = 'fulfilled';
          this.value = value;
          this.handlers.forEach(handler => handler.onFulfilled(value));
        }
      };

      const reject = (reason) => {
        if (this.state === 'pending') {
          this.state = 'rejected';
          this.value = reason;
          this.handlers.forEach(handler => handler.onRejected(reason));
        }
      };

      try {
        executor(resolve, reject);
      } catch (error) {
        reject(error);
      }
    }

    then(onFulfilled, onRejected) {
      return new Promise((resolve, reject) => {
        const handle = () => {
          if (this.state === 'fulfilled') {
            if (onFulfilled) {
              try {
                resolve(onFulfilled(this.value));
              } catch (error) {
                reject(error);
              }
            } else {
              resolve(this.value);
            }
          } else if (this.state === 'rejected') {
            if (onRejected) {
              try {
                resolve(onRejected(this.value));
              } catch (error) {
                reject(error);
              }
            } else {
              reject(this.value);
            }
          } else {
            this.handlers.push({ onFulfilled, onRejected });
          }
        };

        if (this.state !== 'pending') {
          setTimeout(handle, 0);
        } else {
          this.handlers.push({
            onFulfilled: (value) => {
              if (onFulfilled) {
                try {
                  resolve(onFulfilled(value));
                } catch (error) {
                  reject(error);
                }
              } else {
                resolve(value);
              }
            },
            onRejected: (reason) => {
              if (onRejected) {
                try {
                  resolve(onRejected(reason));
                } catch (error) {
                  reject(error);
                }
              } else {
                reject(reason);
              }
            }
          });
        }
      });
    }

    catch(onRejected) {
      return this.then(null, onRejected);
    }

    static resolve(value) {
      return new Promise(resolve => resolve(value));
    }

    static reject(reason) {
      return new Promise((_, reject) => reject(reason));
    }
  };
}

// Polyfill for Symbol if not available
if (typeof global.Symbol === 'undefined') {
  global.Symbol = function Symbol(description) {
    return `__symbol_${description || 'unknown'}_${Math.random().toString(36).substr(2, 9)}`;
  };

  global.Symbol.for = function(key) {
    return `__symbol_for_${key}`;
  };

  global.Symbol.iterator = global.Symbol('iterator');
  global.Symbol.asyncIterator = global.Symbol('asyncIterator');
}

// Prevent errors from missing modules
const originalConsoleError = global.console.error;
global.console.error = function(...args) {
  const message = args[0];
  if (typeof message === 'string' && message.includes('require') && message.includes('hermes')) {
    // Suppress Hermes-related require errors
    return;
  }
  return originalConsoleError.apply(this, args);
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {};
}

console.log('JSC Compatibility polyfills loaded successfully');
