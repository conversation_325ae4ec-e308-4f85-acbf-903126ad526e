import React, { createContext, useContext, useState } from 'react';

// Define the types for our context
interface ProfileData {
  // Personal Information
  fullName: string;
  photoUri: string;
  gender: string;
  dateOfBirth: string;
  nationality: string;
  location: string;
  phone: string;
  email: string;
  whatsapp?: string;
  linkedin?: string;
  website?: string;

  // Job Preferences
  jobType: string;
  jobLocation: string;
  preferredCountries: string[];
  preferredRoles: string[];
  expectedSalary: string;
  willingToRelocate: boolean;
  availableFrom: string;

  // Education
  degree: string;
  institution: string;
  graduationYear: string;
  gpa?: string;

  // Skills
  skills: string[];
  primarySkills: string[];
  secondarySkills: string[];
  tools: string[];
  
  // Work Experience
  experiences: {
    title: string;
    company: string;
    location: string;
    startDate: string;
    endDate: string;
    description: string;
    isCurrent: boolean;
  }[];
  
  // Other fields as needed
}

interface OnboardingContextType {
  profileData: ProfileData;
  updateProfileData: (data: Partial<ProfileData>) => void;
  updateSection: (section: string, data: any) => void;
  resetData: () => void;
}

// Create a default profile data object
const defaultProfileData: ProfileData = {
  fullName: '',
  photoUri: '',
  gender: '',
  dateOfBirth: '',
  nationality: '',
  location: '',
  phone: '',
  email: '',
  whatsapp: '',
  linkedin: '',
  website: '',
  
  jobType: 'Full-time',
  jobLocation: 'Remote',
  preferredCountries: ['United States', 'Canada', 'United Kingdom'],
  preferredRoles: [],
  expectedSalary: '',
  willingToRelocate: false,
  availableFrom: '',
  
  degree: '',
  institution: '',
  graduationYear: '',
  gpa: '',
  
  skills: ['JavaScript', 'React Native', 'UI/UX Design', 'Node.js', 'Git'],
  primarySkills: [],
  secondarySkills: [],
  tools: [],
  
  experiences: [],
};

// Create the context
const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

// Create a provider component
export const OnboardingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [profileData, setProfileData] = useState<ProfileData>(defaultProfileData);

  const updateProfileData = (data: Partial<ProfileData>) => {
    setProfileData(prevData => ({
      ...prevData,
      ...data,
    }));
  };

  const updateSection = (section: string, data: any) => {
    setProfileData(prevData => ({
      ...prevData,
      [section]: data,
    }));
  };

  const resetData = () => {
    setProfileData(defaultProfileData);
  };

  return (
    <OnboardingContext.Provider value={{ profileData, updateProfileData, updateSection, resetData }}>
      {children}
    </OnboardingContext.Provider>
  );
};

// Create a hook to use the context
export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
};